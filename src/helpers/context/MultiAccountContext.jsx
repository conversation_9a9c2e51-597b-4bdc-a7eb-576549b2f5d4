import { createContext, useContext, useState, useEffect } from "react";
import { URL } from "../constant/Url";
import apiInstance from "../Axios/axiosINstance";
import siteConstant from "../constant/siteConstant";
import { fetchFromStorage, saveToStorage } from "./storage";

const MultiAccountContext = createContext();

export const MultiAccountProvider = ({ children }) => {
  const [selectedAccount, setSelectedAccount] = useState(null);
  const [storedAccounts, setStoredAccounts] = useState([]);
  const [loadingAccounts, setLoadingAccounts] = useState(true);
  const [accountsLoaded, setAccountsLoaded] = useState(false);
  const [userToken, setUserToken] = useState(null);
  const [switchingAccount, setSwitchingAccount] = useState(false);

  // Effect to track user token changes
  useEffect(() => {
    const checkToken = () => {
      const userData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
      const token = userData?.token;
      
      // If token changes or is removed, reset account selection
      if (token !== userToken) {
        console.log("Token changed, resetting account selection");
        setUserToken(token);
        if (!token) {
          // User logged out - reset everything
          setSelectedAccount(null);
          setStoredAccounts([]);
          setAccountsLoaded(false);
          localStorage.removeItem("SelectedAccountId");
        } else {
          // User logged in - trigger account fetch
          setAccountsLoaded(false);
          setLoadingAccounts(true);
        }
      }
    };

    // Check immediately
    checkToken();

    // Set up interval to check for token changes
    const intervalId = setInterval(checkToken, 1000);

    return () => {
      clearInterval(intervalId);
    };
  }, [userToken]);

  // Load stored accounts from localStorage
  useEffect(() => {
    const loadStoredAccounts = () => {
      try {
        const storedAccountsData = localStorage.getItem("storedAccounts");
        let accounts = [];
        
        if (storedAccountsData) {
          accounts = JSON.parse(storedAccountsData);
        } else {
          // Initialize with current user's account if no stored accounts exist
          const currentUserData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
          if (currentUserData && currentUserData.token) {
            const currentAccount = {
              userId: currentUserData.user_id || currentUserData.id,
              name: currentUserData.name,
              username: currentUserData.username,
              profileImage: currentUserData.profile_image || "",
              token: currentUserData.token,
              email: currentUserData.email || "",
              brandId: currentUserData.brand_id || currentUserData.brandId || 1, // Try multiple possible brand ID fields
              isMainAccount: true, // Mark as main account
            };
            accounts = [currentAccount];
            localStorage.setItem("storedAccounts", JSON.stringify(accounts));
          }
        }
        
        setStoredAccounts(accounts);
        setAccountsLoaded(true);
      } catch (error) {
        console.error("Error loading stored accounts:", error);
      } finally {
        setLoadingAccounts(false);
      }
    };

    if (userToken) {
      loadStoredAccounts();
    }
  }, [userToken]);

  // Separate effect for account selection that only runs after accounts are loaded
  useEffect(() => {
    if (!accountsLoaded) {
      return;
    }

    console.log("Accounts loaded, selecting account...");
    console.log("Available accounts:", storedAccounts);
    console.log("Stored SelectedAccountId in localStorage:", localStorage.getItem("SelectedAccountId"));

    const storedAccountId = localStorage.getItem("SelectedAccountId");
    if (!storedAccountId) {
      console.log("No stored account ID found");
      // Select first account by default for new users
      const firstAccount = storedAccounts[0];
      if (firstAccount) {
        console.log("Selecting first account:", firstAccount);
        setSelectedAccount(firstAccount);
        localStorage.setItem("SelectedAccountId", firstAccount.userId.toString());
      }
      return;
    }

    const accountId = parseInt(storedAccountId, 10);
    console.log("Looking for account with ID:", accountId);
    
    const accountToSelect = storedAccounts.find((a) => a.userId === accountId);
    console.log("Found account:", accountToSelect);

    if (accountToSelect) {
      console.log("Setting selected account to:", accountToSelect);
      setSelectedAccount(accountToSelect);
    } else {
      console.log("Stored account not found in available accounts");
      // Only fall back to first account if the stored account is not found
      const firstAccount = storedAccounts[0];
      console.log("Falling back to first account:", firstAccount);
      setSelectedAccount(firstAccount);
      if (firstAccount) {
        localStorage.setItem("SelectedAccountId", firstAccount.userId.toString());
      }
    }
  }, [accountsLoaded, storedAccounts, userToken]);

  // Effect to ensure current user is always in the stored accounts list
  useEffect(() => {
    if (!userToken || storedAccounts.length === 0) {
      return;
    }

    const currentUserData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
    const currentUserId = currentUserData?.user_id || currentUserData?.id;
    
    // Check if current user is in stored accounts
    const currentUserInStored = storedAccounts.find(account => account.userId === currentUserId);
    
    if (!currentUserInStored && currentUserData) {
      // Add current user to stored accounts if not present
      const currentAccount = {
        userId: currentUserId,
        name: currentUserData.name,
        username: currentUserData.username,
        profileImage: currentUserData.profile_image || "",
        token: currentUserData.token,
        email: currentUserData.email || "",
        brandId: currentUserData.brand_id || currentUserData.brandId || 1, // Try multiple possible brand ID fields
        isMainAccount: true, // Mark as main account
      };
      
      const updatedAccounts = [...storedAccounts, currentAccount];
      setStoredAccounts(updatedAccounts);
      localStorage.setItem("storedAccounts", JSON.stringify(updatedAccounts));
    } else if (currentUserInStored && !currentUserInStored.isMainAccount) {
      // Ensure the current user is marked as main account
      const updatedAccounts = storedAccounts.map(account => 
        account.userId === currentUserId 
          ? { ...account, isMainAccount: true }
          : account
      );
      setStoredAccounts(updatedAccounts);
      localStorage.setItem("storedAccounts", JSON.stringify(updatedAccounts));
    }
  }, [userToken, storedAccounts]);

  const handleAccountSelect = async (account) => {
    console.log("Handling account selection:", account);
    console.log("All stored accounts:", storedAccounts);
    setSwitchingAccount(true);
    
    try {
      // Validate account data
      if (!account || !account.userId) {
        console.error("Invalid account data:", account);
        return;
      }
      
      // Get the main account (initially logged-in user)
      const mainAccount = storedAccounts.find(acc => acc.isMainAccount);
      const mainUserId = mainAccount?.userId;
      
      console.log("Main account found:", mainAccount);
      
      if (!mainUserId) {
        console.error("Main account not found");
        return;
      }

      // Determine the brand ID to use - try multiple possible fields and ensure it's a number
      const rawBrandId = account?.brandId || account?.brand_id || mainAccount?.brandId || mainAccount?.brand_id || 1;
      const targetBrandId = parseInt(rawBrandId, 10) || 1;
      console.log("Switching to account:", {
        accountId: account.userId,
        accountName: account.name,
        targetBrandId: targetBrandId,
        mainUserId: mainUserId,
        mainAccountBrandId: mainAccount?.brandId,
        isSwitchingToMain: account.isMainAccount
      });

      // Call switch-user-account API with main user ID and target brand ID
      const mainUserIdNum = parseInt(mainUserId, 10);
      console.log("Making API call to switch-user-account with:", {
        user: mainUserIdNum,
        brand: targetBrandId
      });
      
      const switchResponse = await apiInstance.get(URL.SWITCH_USER_ACCOUNT, {
        headers: {
          user: mainUserIdNum, // Main account's user ID as number
          brand: targetBrandId, // Use determined brand ID
        },
      });

      console.log("Switch API response:", switchResponse.data);

      if (switchResponse.data?.status) {
        // Call test-permissions API
        console.log("Making API call to test-permissions with:", {
          user: account.userId,
          brand: targetBrandId
        });
        
        const permissionsResponse = await apiInstance.get(URL.TEST_PERMISSIONS, {
          headers: {
            user: account.userId, // Use the switched account's user ID for permissions
            brand: targetBrandId, // Use determined brand ID
          },
        });

        console.log("Permissions API response:", permissionsResponse.data);

        if (permissionsResponse.data?.status) {
          // Update selected account
          setSelectedAccount(account);
          localStorage.setItem("SelectedAccountId", account?.userId?.toString());
          
          // Update the current user data in storage with the selected account's data
          if (account) {
            const currentUserData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
            const updatedUserData = {
              ...currentUserData,
              token: account.token,
              userId: account.userId,
              name: account.name,
              username: account.username,
              profile_image: account.profileImage,
            };
            saveToStorage(siteConstant?.INDENTIFIERS?.USERDATA, updatedUserData);
          }
          
          console.log("Account switched successfully");
        } else {
          console.error("Permissions check failed");
        }
      } else {
        console.error("Account switch failed");
      }
    } catch (error) {
      console.error("Error switching account:", error);
    } finally {
      setSwitchingAccount(false);
    }
  };

  const addStoredAccount = (account) => {
    console.log("Adding stored account:", account);
    const newAccounts = [...storedAccounts, account];
    setStoredAccounts(newAccounts);
    localStorage.setItem("storedAccounts", JSON.stringify(newAccounts));
  };

  const removeStoredAccount = (accountId) => {
    console.log("Removing stored account:", accountId);
    const filteredAccounts = storedAccounts.filter(account => account.userId !== accountId);
    setStoredAccounts(filteredAccounts);
    localStorage.setItem("storedAccounts", JSON.stringify(filteredAccounts));
    
    // If the removed account was the selected one, select the first available account
    if (selectedAccount && selectedAccount.userId === accountId) {
      const firstAccount = filteredAccounts[0];
      if (firstAccount) {
        setSelectedAccount(firstAccount);
        localStorage.setItem("SelectedAccountId", firstAccount.userId.toString());
      } else {
        setSelectedAccount(null);
        localStorage.removeItem("SelectedAccountId");
      }
    }
  };

  const resetAccounts = () => {
    setSelectedAccount(null);
    setStoredAccounts([]);
    localStorage.removeItem("SelectedAccountId");
    localStorage.removeItem("storedAccounts");
  };

  const getCurrentAccount = () => {
    return selectedAccount;
  };

  const getStoredAccounts = () => {
    return storedAccounts;
  };

  return (
    <MultiAccountContext.Provider
      value={{
        selectedAccount,
        handleAccountSelect,
        storedAccounts,
        addStoredAccount,
        removeStoredAccount,
        resetAccounts,
        loadingAccounts,
        switchingAccount,
        getCurrentAccount,
        getStoredAccounts,
      }}
    >
      {children}
    </MultiAccountContext.Provider>
  );
};

export const useMultiAccount = () => useContext(MultiAccountContext); 